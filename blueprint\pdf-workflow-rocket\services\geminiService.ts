import { GoogleGenAI, Type } from "@google/genai";
import type { ExtractedData } from '../types';

if (!process.env.API_KEY) {
    throw new Error("API_KEY environment variable not set");
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

const responseSchema = {
    type: Type.OBJECT,
    properties: {
        fileName: {
            type: Type.STRING,
            description: "The name of the source file, as provided in the prompt."
        },
        extractedKeywords: {
            type: Type.ARRAY,
            description: "An array of the keywords that were used for the search. Should be an empty array if no keywords were provided.",
            items: {
                type: Type.STRING
            }
        },
        structuredData: {
            type: Type.ARRAY,
            description: "The structured data extracted from the document, as key-value pairs.",
            items: {
                type: Type.OBJECT,
                properties: {
                    key: {
                        type: Type.STRING,
                        description: "The label or key for the extracted piece of information (e.g., 'Invoice Number', 'Total Amount', 'Client Name')."
                    },
                    value: {
                        type: Type.STRING,
                        description: "The corresponding value for the key."
                    }
                },
                required: ["key", "value"]
            }
        }
    },
    required: ["fileName", "extractedKeywords", "structuredData"]
};

export const transformPdf = async (
  fileName: string,
  mimeType: string,
  fileData: string, // base64 encoded string
  keywords: string
): Promise<ExtractedData> => {
  const prompt = `
    You are an advanced data extraction engine for a SaaS tool called 'PDF Workflow Rocket'.
    Your primary task is to meticulously analyze the provided document and extract structured information from it.

    The document to analyze is provided as a file part in this request.

    The user has specified the following keywords for targeted extraction: "${keywords || 'None'}".
    If keywords are provided, prioritize finding data points corresponding to these keywords.
    If no keywords are provided ('None'), perform a comprehensive extraction of all salient key-value pairs you can identify in the document (e.g., invoice numbers, dates, names, addresses, line items, totals, etc.).

    Your output must be a JSON object that adheres to the provided schema.
    - The "fileName" field in your JSON response must be exactly: "${fileName}".
    - The "extractedKeywords" field should be an array of strings based on the user's comma-separated input: "${keywords}". If the input string is empty, the array should be empty.
    - The "structuredData" field should contain the key-value pairs you extract from the document content.
  `;

  const filePart = {
    inlineData: {
      mimeType: mimeType,
      data: fileData,
    },
  };

  const textPart = {
    text: prompt,
  };

  try {
    const response = await ai.models.generateContent({
        model: "gemini-2.5-flash",
        contents: { parts: [textPart, filePart] },
        config: {
            responseMimeType: "application/json",
            responseSchema: responseSchema,
        },
    });

    const jsonString = response.text.trim();
    const parsedData = JSON.parse(jsonString) as ExtractedData;
    return parsedData;

  } catch (error) {
    console.error("Error transforming document with Gemini:", error);
    if (error instanceof Error) {
        throw new Error(`Failed to process document: ${error.message}`);
    }
    throw new Error("An unknown error occurred while processing the document.");
  }
};
