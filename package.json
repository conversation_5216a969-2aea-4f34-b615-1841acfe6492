{"name": "integrate-pdf", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.27.1", "@google/genai": "^1.11.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@react-three/drei": "^10.6.1", "@react-three/fiber": "^9.2.0", "@sentry/nextjs": "^9.42.0", "@supabase/supabase-js": "^2.52.1", "@types/three": "^0.178.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.1", "framer-motion": "^12.23.9", "inngest": "^3.40.1", "lucide-react": "^0.525.0", "next": "15.4.4", "pdf-parse": "^1.1.1", "react": "19.1.0", "react-dom": "19.1.0", "sonner": "^2.0.6", "svix": "^1.69.0", "tailwind-merge": "^3.3.1", "three": "^0.178.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}